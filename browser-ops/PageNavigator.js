import { PAGE_OPTIONS } from '../config.js';
import { handleError } from '../utils/GlobalErrorHandler.js';
import {
  closeModals,
  findModals,
  isScrollingBlocked,
} from '../core/ModalHandler.js';

/**
 * Navigate to URL and wait for page to stabilize
 * @param {Object} page - Playwright page object
 * @param {string} url - The URL to scrape
 * @returns {Promise<boolean>} - Success status
 */
export async function navigateToUrl(page, url) {
  const maxRetries = 2;
  let lastError = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🌐 Navigating to: ${url} (attempt ${attempt}/${maxRetries})`);
      const BLOCKED_RESOURCE_TYPES = ['media', 'font']; // 'media' covers audio and video

      // Set up resource blocking with error handling
      await page.route('**/*', (route) => {
        try {
          if (BLOCKED_RESOURCE_TYPES.includes(route.request().resourceType())) {
            route.abort();
          } else {
            route.continue();
          }
        } catch (routeError) {
          console.warn('⚠️ Route handling error:', routeError.message);
          try {
            route.continue();
          } catch (fallbackError) {
            // Ignore fallback errors
          }
        }
      });

      // Navigate with timeout
      const navigationTimeout = PAGE_OPTIONS.timeout || 30000;
      await page.goto(url, {
        ...PAGE_OPTIONS,
        timeout: navigationTimeout
      });

      // Wait for page to be ready with progressive timeouts
      await Promise.race([
        page.waitForLoadState('networkidle', { timeout: 15000 }),
        new Promise(resolve => setTimeout(resolve, 10000)) // Fallback after 10s
      ]);

      await page.waitForLoadState('domcontentloaded', { timeout: 5000 });

      console.log('✅ Page loaded successfully');

      const scrollingBlocked = await isScrollingBlocked(page);
      console.log(`📜 Scrolling blocked: ${scrollingBlocked}`);
      if (scrollingBlocked) {
        console.log('🔄 Handling overlay modals...');
        await page.keyboard.press('Escape', { delay: 10 });
        await page.click('html', { delay: 100, force: true });
        await page.waitForTimeout(500);
      } else {
        await page.waitForTimeout(500);
      }

      const modals = await findModals(page);
      if (modals.length === 0) {
        console.log('✨ No modals found, continuing...');
      } else {
        console.log(`🎭 Found ${modals.length} modal(s), attempting to close...`);
        const modalsClosed = await closeModals(page, modals);

        if (modalsClosed && modals.length > 0) {
          console.log('✅ Modals closed successfully');
        } else if (modals.length > 0) {
          console.log('⚠️ Some modals could not be closed');
        }
      }

      // If we reach here, navigation was successful
      return true;

    } catch (error) {
      lastError = error;
      console.warn(`⚠️ Navigation attempt ${attempt} failed:`, error.message);

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * attempt, 5000); // Progressive delay up to 5s
        console.log(`🔄 Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All attempts failed
  await handleError(lastError || new Error('Navigation failed after all retries'), {
    operation: 'navigateToUrl',
    url,
    attempts: maxRetries,
  });
  return false;
}
