import { chromium as chromiumCore } from 'playwright-core';
import { BROWSER_OPTIONS } from '../config.js'
import { handleError } from '../utils/GlobalErrorHandler.js'
import dotenv from 'dotenv'

dotenv.config();

let browser = null;
let page = null;

/**
 * Initialize browser and page
 * @returns {Promise<boolean>} Success status
 */
export async function initBrowser() {
  try {
    console.log('🖥️ Launching local browser...');
    browser = await chromiumCore.launch({ ...BROWSER_OPTIONS });
    console.log('✅ Local browser launched successfully');

    // Create new page
    page = await browser.newPage({
      viewport: BROWSER_OPTIONS.viewport,
    });
    console.log('📄 New page created successfully');

    return true;
  } catch (error) {
    await handleError(error, {
      operation: 'initBrowser',
    });
    return false;
  }
}

/**
 * Get the current page instance
 * @returns {Object|null} Current page instance
 */
export function getPage() {
  return page;
}

/**
 * Get the current browser instance
 * @returns {Object|null} Current browser instance
 */
export function getBrowser() {
  return browser;
}

/**
 * Close browser and clean up
 * @returns {Promise<void>}
 */
export async function closeBrowser() {
  if (browser) {
    try {
      await browser.close();
      console.log('✅ Local browser closed successfully');
    } catch (error) {
      console.error('⚠️ Error closing browser:', error.message);
    } finally {
      browser = null;
      page = null;
    }
  }
}
