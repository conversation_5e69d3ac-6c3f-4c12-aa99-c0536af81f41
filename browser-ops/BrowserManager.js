import { chromium as chromiumCore } from 'playwright-core';
import { BROWSER_OPTIONS } from '../config.js'
import { handleError } from '../utils/GlobalErrorHandler.js'
import dotenv from 'dotenv'

dotenv.config();

let browser = null;
let page = null;

/**
 * Initialize browser and page
 * @returns {Promise<boolean>} Success status
 */
export async function initBrowser() {
  try {
    console.log('🖥️ Launching local browser...');
    browser = await chromiumCore.launch({ ...BROWSER_OPTIONS });
    console.log('✅ Local browser launched successfully');

    // Create new page
    page = await browser.newPage({
      viewport: BROWSER_OPTIONS.viewport,
    });
    console.log('📄 New page created successfully');

    return true;
  } catch (error) {
    await handleError(error, {
      operation: 'initBrowser',
    });
    return false;
  }
}

/**
 * Get the current page instance
 * @returns {Object|null} Current page instance
 */
export function getPage() {
  return page;
}

/**
 * Get the current browser instance
 * @returns {Object|null} Current browser instance
 */
export function getBrowser() {
  return browser;
}

/**
 * Close browser and clean up
 * @returns {Promise<void>}
 */
export async function closeBrowser() {
  if (browser) {
    try {
      // Close all pages first
      const pages = await browser.pages();
      await Promise.all(pages.map(async (p) => {
        try {
          await p.close();
        } catch (error) {
          console.warn('⚠️ Error closing page:', error.message);
        }
      }));

      // Close browser
      await browser.close();
      console.log('✅ Local browser closed successfully');
    } catch (error) {
      console.error('⚠️ Error closing browser:', error.message);
    } finally {
      browser = null;
      page = null;
    }
  }
}

/**
 * Force close browser (for emergency cleanup)
 * @returns {Promise<void>}
 */
export async function forceCloseBrowser() {
  if (browser) {
    try {
      // Force kill browser process
      await browser.close({ force: true });
      console.log('✅ Browser force closed');
    } catch (error) {
      console.error('⚠️ Error force closing browser:', error.message);
    } finally {
      browser = null;
      page = null;
    }
  }
}
