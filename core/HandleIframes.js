/**
 * @param {import('playwright').Page} page - The Playwright page object
 * @returns {Promise<void>}
 */
export async function handleIframes(page) {
  const frames = page.frames();

  for (const frame of frames) {
    if (frame === page.mainFrame()) continue;

    try {
      const iframeElement = await frame.frameElement();
      if (!iframeElement) continue;

      const iframeSrc = await iframeElement.evaluate(
        iframe => iframe.src || iframe.getAttribute('srcdoc') || 'inline'
      );

      try {
        await frame.waitForLoadState('domcontentloaded', { timeout: 2000 });
      } catch (timeoutError) {
        console.log(
          `Iframe load timeout for: ${iframeSrc}, proceeding anyway...${timeoutError}`
        );
      }

      const frameContent = await frame.evaluate(() => {
        return document.documentElement.outerHTML;
      }, {});

      if (frameContent && frameContent.trim().length > 0) {
        await iframeElement.evaluate(
          (iframe, config) => {
            const container = document.createElement('div');
            container.className = 'iframe-content-replacement';
            container.setAttribute(
              'data-original-iframe-src',
              config.iframeSrc || ''
            );
            container.innerHTML = config.content;
            iframe.parentNode.replaceChild(container, iframe);
          },
          { content: frameContent, iframeSrc }
        );
      } else {
        console.log(`No content found in iframe: ${iframeSrc}`);
      }
    } catch (e) {
      const frameUrl = frame.url();
      console.log(`❌ Cannot access iframe (${frameUrl}): ${e.message}`);

      if (e.message.includes('cross-origin')) {
        console.log('   → Cross-origin restriction detected');
      } else if (e.message.includes('timeout')) {
        console.log('   → Frame loading timeout');
      }
    }
  }
}
