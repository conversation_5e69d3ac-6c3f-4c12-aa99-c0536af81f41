import dotenv from 'dotenv';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { streamText } from 'ai';
import { LLM_MODEL_CONFIG } from '../config.js';
import {
  generateHTMLToMarkdownPrompt,
  generateMarkdownToMarkdownPrompt,
} from '../utils/AIPrompts.js';
import { handleError } from '../utils/GlobalErrorHandler.js';
import { createSimpleLogger } from '../utils/SimpleLogger.js';

dotenv.config();

/**
 * Convert HTML to markdown using AI with progress indicators
 * @param {string} htmlContent - The HTML content to convert
 * @param {string} userQuery - Optional user query for specific content focus
 * @param {Object} logger - Optional logger for progress updates
 * @returns {Promise<string>} - The AI-converted markdown content
 */
export async function convertAndImproveMarkdownFromHTML(
  htmlContent,
  userQuery = '',
  logger = null
) {
  if (!htmlContent.trim()) {
    console.warn('⚠️ HTML content is empty');
    return '';
  }

  // Use provided logger or create a new one
  const simpleLogger = logger || createSimpleLogger();

  // Initialize Google AI
  const google = createGoogleGenerativeAI({
    apiKey: process.env.GOOGLE_AI_API_KEY,
  });

  let streamingComplete = false;
  let convertedMarkdown = '';

  try {
    simpleLogger.setAction('Converting HTML to markdown with AI');

    const stream = await streamText({
      model: google(LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash'),
      temperature: 0.9,
      // providerOptions: {
      //   google: {
      //     thinkingConfig: {
      //       thinkingBudget: 1024,
      //     },
      //   },
      // },
      experimental_telemetry: {
        isEnabled: true,
        functionId: 'convertAndImproveMarkdownFromHTML',
      },
      prompt: generateHTMLToMarkdownPrompt(userQuery, htmlContent),
      onFinish: () => {
        streamingComplete = true;
      },
    });

    simpleLogger.updateAction('Processing AI response for HTML conversion');

    // Process the streaming response
    for await (const chunk of stream.textStream) {
      convertedMarkdown += chunk;
    }

    // Wait for streaming to complete
    while (!streamingComplete) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!convertedMarkdown.trim()) {
      simpleLogger.error('AI returned empty markdown');
      return '';
    }

    simpleLogger.complete(
      `HTML to Markdown conversion complete! Generated ${convertedMarkdown.length} characters`,
      {
        outputLength: convertedMarkdown.length,
        inputLength: htmlContent.length,
      }
    );

    return convertedMarkdown;
  } catch (error) {
    simpleLogger.error(error, 'AI HTML to Markdown conversion failed');

    const handledError = await handleError(error, {
      operation: 'convertAndImproveMarkdown',
      htmlLength: htmlContent.length,
      userQuery: userQuery || 'none',
      modelUsed: LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash',
    });

    console.log(`🔍 Error ID: ${handledError.stackId}`);
    return '';
  }
}

/**
 * Convert Markdown to markdown using AI with progress indicators
 * @param {string} markdownContent - The markdown content to convert
 * @param {string} userQuery - Optional user query for specific content focus
 * @param {Object} logger - Optional logger for progress updates
 * @returns {Promise<string>} - The AI-converted markdown content
 */
export async function convertAndImproveMarkdownFromMarkdown(
  markdownContent,
  userQuery = '',
  logger = null
) {
  if (!markdownContent.trim()) {
    console.warn('⚠️ Markdown content is empty');
    return '';
  }

  // Use provided logger or create a new one
  const simpleLogger = logger || createSimpleLogger();

  // Initialize Google AI
  const google = createGoogleGenerativeAI({
    apiKey: process.env.GOOGLE_AI_API_KEY,
  });

  let streamingComplete = false;
  let convertedMarkdown = '';

  try {
    simpleLogger.setAction('Improving Markdown from markdown content with AI');

    const stream = await streamText({
      model: google(LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash'),
      temperature: 0.9,
      // providerOptions: {
      //   google: {
      //     thinkingConfig: {
      //       thinkingBudget: 1024,
      //     },
      //   },
      // },
      prompt: generateMarkdownToMarkdownPrompt(userQuery, markdownContent),
      experimental_telemetry: {
        isEnabled: true,
        functionId: 'convertAndImproveMarkdownFromMarkdown',
      },
      onFinish: () => {
        streamingComplete = true;
      },
    });

    simpleLogger.updateAction('Processing AI response for Markdown improvement');

    // Process the streaming response
    for await (const chunk of stream.textStream) {
      convertedMarkdown += chunk;
    }

    // Wait for streaming to complete
    while (!streamingComplete) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!convertedMarkdown.trim()) {
      simpleLogger.error('AI returned empty Markdown');
      return '';
    }

    simpleLogger.complete(
      `Markdown improvement complete! Generated ${convertedMarkdown.length} characters`,
      {
        outputLength: convertedMarkdown.length,
        inputLength: markdownContent.length,
      }
    );

    return convertedMarkdown;
  } catch (error) {
    simpleLogger.error(error, 'AI Markdown improvement failed');

    const handledError = await handleError(error, {
      operation: 'convertAndImproveMarkdown',
      markdownLength: markdownContent.length,
      userQuery: userQuery || 'none',
      modelUsed: LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash',
    });

    console.log(`🔍 Error ID: ${handledError.stackId}`);
    return '';
  }
}
