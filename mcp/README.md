# Page Extractor MCP Server

A simple Model Context Protocol (MCP) server that provides web scraping capabilities with detailed progress tracking. This server integrates with MCP-compatible clients like <PERSON> to extract and convert web page content to markdown format.

## Features

- **Simple & Clean**: One tool that does everything you need
- **Progress Tracking**: Detailed timeline of scraping progress with timestamps
- **Multiple Scraping Modes**: Normal (fast) and Beast (thorough) modes
- **Smart Content Extraction**: AI-powered content extraction with query-based filtering
- **Comprehensive Results**: Full progress log with final extracted content
- **MCP Integration**: Full compatibility with MCP clients and tools

## Tool Available

### `scrape_page_stream`
Scrapes a web page with detailed progress tracking and returns the complete timeline with extracted content.

**Parameters:**
- `url` (required): The URL to scrape
- `query` (optional): Specific content to focus extraction on
- `mode` (optional): `normal` (default) or `beast` for more thorough extraction
- `baseUrl` (optional): Base URL of the scraping service (default: http://localhost:6000)

**Returns:**
- Complete progress timeline with timestamps and emojis
- Final extracted content in markdown format
- Processing statistics (time, steps, content length)
- Success/error status with detailed information

## Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Running page extraction service (see main project README)

### Quick Setup

1. **Install dependencies:**
   ```bash
   cd mcp
   npm install
   ```

2. **Start the scraping service:**
   ```bash
   # In the main project directory
   npm start
   ```

3. **Configure Claude Desktop:**
   Add to your Claude Desktop config file:
   - **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows**: `%APPDATA%/Claude/claude_desktop_config.json`

   ```json
   {
     "mcpServers": {
       "page-extractor": {
         "command": "node",
         "args": ["/absolute/path/to/your/project/mcp/src/index.js"]
       }
     }
   }
   ```

4. **Restart Claude Desktop** to load the MCP server

## Usage Examples

### Basic Web Scraping
```
Use the scrape_page_stream tool to scrape https://example.com
```

### Targeted Content Extraction
```
Use scrape_page_stream to extract "pricing information" from https://company.com/pricing
```

### Thorough Extraction (Beast Mode)
```
Use scrape_page_stream in beast mode to scrape https://complex-site.com
```

## Sample Output

When you use the tool, you'll get a detailed response like this:

```
# ✅ Scraping Results for https://example.com

## 📊 Summary
- **Status**: Success ✅
- **Content Length**: 2,847 characters
- **Processing Time**: 12s
- **Total Steps**: 8 (6 successful, 0 errors)
- **Mode**: normal
- **Query**: None

## 📋 Progress Timeline
ℹ️ **14:23:45** - Connecting to scraping service...
ℹ️ **14:23:46** - Connected to scraping service, starting process...
⚡ **14:23:47** - Browser setup completed
⚡ **14:23:49** - Navigating to URL...
⚡ **14:23:52** - Page loaded, analyzing content...
⚡ **14:23:55** - AI analysis completed
✅ **14:23:57** - Final result received
   Content: 2847 chars
✅ **14:23:57** - Scraping process completed

## 📄 Extracted Content

# Example Domain
This domain is for use in illustrative examples...
[Full extracted content here]
```

## Configuration

### Environment Variables
- `SCRAPING_SERVICE_URL`: URL of the page extraction service (default: http://localhost:6000)
- `SCRAPING_TIMEOUT`: Timeout in milliseconds (default: 300000 = 5 minutes)

### Scraping Modes
- **Normal**: Fast extraction suitable for most websites
- **Beast**: Thorough extraction with AI analysis, better for complex sites

## Troubleshooting

### Common Issues

1. **"Connection refused" errors**
   - Ensure the main scraping service is running: `npm start`
   - Check that the service is accessible at http://localhost:6000

2. **"Tool not found" errors**
   - Verify the absolute path in claude_desktop_config.json
   - Restart Claude Desktop after configuration changes

3. **Timeout errors**
   - Try using 'normal' mode instead of 'beast' mode
   - Check if the target website is accessible

4. **Permission errors**
   - Ensure Node.js has permission to run the script
   - Check file permissions: `chmod +x mcp/src/index.js`

### Getting Help
- Check Claude Desktop developer console for detailed error logs
- Verify the scraping service is running and accessible
- Test the target URL in your browser first

## Development

### File Structure
```
mcp/
├── src/
│   └── index.js          # Main MCP server implementation
├── package.json          # Dependencies and scripts
└── README.md            # This file
```

### Running in Development
```bash
cd mcp
node src/index.js
```

### Testing the Server
```bash
# Test with a simple request
echo '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' | node src/index.js
```

## How It Works

1. **MCP Integration**: The server implements the Model Context Protocol to communicate with Claude Desktop
2. **Streaming Support**: Connects to the page extraction service's streaming endpoint
3. **Progress Tracking**: Captures and formats all progress updates with timestamps
4. **Result Formatting**: Presents the complete timeline and extracted content in a readable format

