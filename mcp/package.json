{"name": "page-extractor-mcp-server", "version": "1.0.0", "description": "MCP server for page extraction with streaming support", "type": "module", "main": "src/index.js", "bin": {"page-extractor-mcp": "./src/index.js"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "build": "echo 'No build step required'", "test": "node test/test-server.js"}, "keywords": ["mcp", "model-context-protocol", "web-scraping", "streaming", "sse"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "node-fetch": "^3.3.2", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^20.11.0"}, "mcp": {"server": {"name": "page-extractor", "version": "1.0.0"}}}