/**
 * Simple Logger - Clean, minimal logging system for essential information only
 * Shows only important progress updates while maintaining streaming functionality
 */
export class SimpleLogger {
  constructor(progressCallback = null) {
    this.progressCallback = progressCallback;
    this.isStreaming = progressCallback !== null;
    this.currentAction = null;
    this.startTime = Date.now();
    this.lastUpdate = 0;
    this.updateInterval = 2000; // Fixed 2-second intervals
    this.updateTimer = null;
    this.silent = false; // Option to suppress console output

    if (!this.silent) {
      console.log('🚀 Starting scraping process...');
    }
  }

  /**
   * Set the current action being performed
   * @param {string} action - Clear description of current action
   */
  setAction(action) {
    this.currentAction = action;
    this.lastUpdate = 0; // Force immediate update
    this._sendUpdate();
    this._startPeriodicUpdates();
  }

  /**
   * Enable or disable console output
   * @param {boolean} silent - Whether to suppress console output
   */
  setSilent(silent = true) {
    this.silent = silent;
  }

  /**
   * Update the current action (if different)
   * @param {string} action - New action description
   */
  updateAction(action) {
    if (this.currentAction !== action) {
      this.setAction(action);
    }
  }

  /**
   * Complete the current process
   * @param {string} message - Completion message
   * @param {Object} result - Optional result data
   */
  complete(message = 'Process completed successfully', result = {}) {
    this._stopPeriodicUpdates();

    const elapsed = Date.now() - this.startTime;
    const finalMessage = `✅ ${message}`;

    if (!this.silent) {
      console.log(finalMessage);
      console.log(`⏱️ Total time: ${Math.round(elapsed / 1000)}s`);
    }

    // Don't send 'completed' type messages from SimpleLogger
    // Only the API handler should send the final 'completed' message
    if (this.isStreaming) {
      this._streamData({
        type: 'info',
        message: finalMessage,
        elapsed,
        result
      });
    }
  }

  /**
   * Log an error
   * @param {Error|string} error - Error to log
   * @param {string} context - Additional context
   */
  error(error, context = '') {
    this._stopPeriodicUpdates();

    const errorMessage = error instanceof Error ? error.message : error;
    const fullMessage = context ? `${context}: ${errorMessage}` : errorMessage;

    if (!this.silent) {
      console.error(`❌ ${fullMessage}`);
    }

    if (this.isStreaming) {
      this._streamData({
        type: 'error',
        message: fullMessage,
        error: errorMessage
      });
    }
  }

  /**
   * Log a simple message
   * @param {string} message - Message to log
   * @param {string} level - Log level (info, warn, error)
   */
  log(message, level = 'info') {
    const emoji = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';

    if (!this.silent) {
      console.log(`${emoji} ${message}`);
    }

    if (this.isStreaming) {
      this._streamData({
        type: 'log',
        level,
        message
      });
    }
  }

  /**
   * Send periodic updates every 2 seconds
   * @private
   */
  _sendUpdate() {
    if (!this.currentAction) return;

    const now = Date.now();
    const elapsed = now - this.startTime;

    // Only send if 2 seconds have passed or it's the first update
    if (now - this.lastUpdate >= this.updateInterval || this.lastUpdate === 0) {
      const message = `⚡ ${this.currentAction}`;

      if (!this.silent) {
        console.log(message);
      }

      if (this.isStreaming) {
        this._streamData({
          type: 'progress',
          action: this.currentAction,
          message,
          elapsed
        });
      }

      this.lastUpdate = now;
    }
  }

  /**
   * Start periodic updates every 2 seconds
   * @private
   */
  _startPeriodicUpdates() {
    this._stopPeriodicUpdates();
    
    this.updateTimer = setInterval(() => {
      this._sendUpdate();
    }, this.updateInterval);
  }

  /**
   * Stop periodic updates
   * @private
   */
  _stopPeriodicUpdates() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
  }

  /**
   * Stream data to API if streaming is enabled
   * @param {Object} data - Data to stream
   * @private
   */
  _streamData(data) {
    if (!this.isStreaming || !this.progressCallback) return;
    
    try {
      const streamData = {
        timestamp: new Date().toISOString(),
        ...data
      };
      
      // Handle both sync and async callbacks
      const result = this.progressCallback(streamData);
      if (result && typeof result.then === 'function') {
        result.catch(err => console.error('Streaming error:', err));
      }
    } catch (error) {
      console.error('Error streaming data:', error);
    }
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this._stopPeriodicUpdates();
  }
}

/**
 * Create a new SimpleLogger instance
 * @param {Function} progressCallback - Optional callback for streaming progress
 * @param {Object} options - Logger options
 * @param {boolean} options.silent - Whether to suppress console output (default: false)
 * @returns {SimpleLogger} New logger instance
 */
export function createSimpleLogger(progressCallback = null, options = {}) {
  const logger = new SimpleLogger(progressCallback);
  if (options.silent !== undefined) {
    logger.setSilent(options.silent);
  }
  return logger;
}