import { performance } from 'perf_hooks';
import { cpus, totalmem, freemem } from 'os';

export class SimplePerformanceMonitor {
  constructor() {
    this.startTime = null;
    this.phases = [];
    this.currentPhase = null;
    this.browserPage = null;
    this.monitoringInterval = null;

    this.metrics = {
      startMemory: 0,
      peakMemory: 0,
      systemMemoryStart: 0,
      browserStartMemory: 0,
      browserPeakMemory: 0,
      totalCPUs: cpus().length,
      totalSystemMemory: Math.round(totalmem() / 1024 / 1024),
    };
  }

  start() {
    this.startTime = performance.now();
    this.metrics.startMemory = this._getNodeMemoryMB();
    this.metrics.peakMemory = this.metrics.startMemory;
    this.metrics.systemMemoryStart = this._getSystemMemoryUsedMB();

    console.log(
      `🚀 Monitor started | Node: ${this.metrics.startMemory}MB | CPUs: ${this.metrics.totalCPUs} | System: ${this.metrics.totalSystemMemory}MB`
    );

    this.monitoringInterval = setInterval(() => this._updateMetrics(), 2000);
  }

  startPhase(name) {
    if (this.currentPhase) this.endPhase();

    this.currentPhase = {
      name,
      startTime: performance.now(),
      startMemory: this._getNodeMemoryMB(),
    };

    console.log(`📈 ${name}...`);
  }

  endPhase() {
    if (!this.currentPhase) return;

    const duration = performance.now() - this.currentPhase.startTime;
    const memoryChange =
      this._getNodeMemoryMB() - this.currentPhase.startMemory;

    this.phases.push({
      name: this.currentPhase.name,
      duration: Math.round(duration),
      memoryChange: Math.round(memoryChange * 10) / 10,
    });

    console.log(
      `✅ ${this.currentPhase.name} | ${this._formatDuration(duration)} | ${memoryChange >= 0 ? '+' : ''}${memoryChange.toFixed(1)}MB`
    );
    this.currentPhase = null;
  }

  stop() {
    if (!this.startTime || !this.monitoringInterval) return;

    if (this.currentPhase) this.endPhase();
    clearInterval(this.monitoringInterval);
    this.monitoringInterval = null;

    const totalDuration = performance.now() - this.startTime;
    const finalMemory = this._getNodeMemoryMB();
    const memoryGrowth = finalMemory - this.metrics.startMemory;
    const systemGrowth =
      this._getSystemMemoryUsedMB() - this.metrics.systemMemoryStart;

    console.log('\n📊 Summary:');
    console.log(`⏱️  Total: ${this._formatDuration(totalDuration)}`);
    console.log(
      `🧠 Node: ${this.metrics.startMemory}MB → ${finalMemory}MB (${memoryGrowth >= 0 ? '+' : ''}${memoryGrowth.toFixed(1)}MB, peak: ${this.metrics.peakMemory}MB)`
    );

    if (this.browserPage && this.metrics.browserPeakMemory > 0) {
      const browserCurrent = this.metrics.browserCurrentMemory || 0;
      const browserGrowth = browserCurrent - this.metrics.browserStartMemory;
      console.log(
        `🌐 Browser: ${this.metrics.browserStartMemory}MB → ${browserCurrent}MB (${browserGrowth >= 0 ? '+' : ''}${browserGrowth.toFixed(1)}MB, peak: ${this.metrics.browserPeakMemory}MB)`
      );
    }

    console.log(`🖥️  System: ${systemGrowth >= 0 ? '+' : ''}${systemGrowth}MB`);

    if (this.phases.length > 0) {
      console.log('\n📋 Phases:');
      this.phases.forEach(phase => {
        const percentage = Math.round((phase.duration / totalDuration) * 100);
        console.log(
          `• ${phase.name}: ${this._formatDuration(phase.duration)} (${percentage}%)`
        );
      });
    }

    this._showAssessment(totalDuration, memoryGrowth);
  }

  setBrowserContext(page) {
    this.browserPage = page;
  }

  async _updateMetrics() {
    const currentMemory = this._getNodeMemoryMB();
    if (currentMemory > this.metrics.peakMemory) {
      this.metrics.peakMemory = currentMemory;
    }

    // Update browser memory if available
    if (this.browserPage) {
      const browserMem = await this._getBrowserMemoryMB();
      this.metrics.browserCurrentMemory = browserMem;

      if (this.metrics.browserStartMemory === 0) {
        this.metrics.browserStartMemory = browserMem;
      }

      if (browserMem > this.metrics.browserPeakMemory) {
        this.metrics.browserPeakMemory = browserMem;
      }
    }

    // Log status every 10 seconds
    const elapsed = Math.round((performance.now() - this.startTime) / 1000);
    if (elapsed % 10 === 0) {
      const phaseName = this.currentPhase?.name || 'idle';
      const browserInfo =
        this.browserPage && this.metrics.browserCurrentMemory
          ? ` | Browser: ${this.metrics.browserCurrentMemory}MB`
          : '';
      console.log(
        `⏱️  ${elapsed}s | Node: ${currentMemory}MB${browserInfo} | ${phaseName}`
      );
    }
  }

  _getNodeMemoryMB() {
    return Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 10) / 10;
  }

  _getSystemMemoryUsedMB() {
    return Math.round((totalmem() - freemem()) / 1024 / 1024);
  }

  async _getBrowserMemoryMB() {
    try {
      const client = await this.browserPage
        .context()
        .newCDPSession(this.browserPage);

      // Try to get more comprehensive memory info
      try {
        const memoryInfo = await client.send('Memory.getDOMCounters');
        await client.detach();

        if (memoryInfo && memoryInfo.jsHeapSizeUsed) {
          return (
            Math.round((memoryInfo.jsHeapSizeUsed / 1024 / 1024) * 10) / 10
          );
        }
        // eslint-disable-next-line no-unused-vars
      } catch (_) {
        // Fall back to Runtime.getHeapUsage
        const response = await client.send('Runtime.getHeapUsage');
        await client.detach();

        const usedSize = response?.result?.usedSize || response?.usedSize;
        if (usedSize) {
          // CDP often returns heap usage, but browser total is typically 3-5x higher
          const heapMB = usedSize / 1024 / 1024;
          const estimatedTotalMB = heapMB * 4; // Estimate total process memory
          return Math.round(estimatedTotalMB * 10) / 10;
        }
      }

      throw new Error('No valid memory data from CDP');
      // eslint-disable-next-line no-unused-vars
    } catch (_) {
      // Fallback: estimate based on system memory growth
      const systemGrowth =
        this._getSystemMemoryUsedMB() - this.metrics.systemMemoryStart;
      const nodeGrowth = this._getNodeMemoryMB() - this.metrics.startMemory;
      const estimatedBrowser = Math.max(50, systemGrowth - nodeGrowth); // Minimum 50MB for browser
      return Math.round(estimatedBrowser * 10) / 10;
    }
  }

  _formatDuration(ms) {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60000) return `${Math.round(ms / 100) / 10}s`;

    const minutes = Math.floor(ms / 60000);
    const seconds = Math.round((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }

  _showAssessment(totalDuration, memoryGrowth) {
    const browserGrowth = this.browserPage
      ? (this.metrics.browserCurrentMemory || 0) -
        this.metrics.browserStartMemory
      : 0;
    const totalMemoryGrowth = memoryGrowth + browserGrowth;
    const totalPeakMemory =
      this.metrics.peakMemory + (this.metrics.browserPeakMemory || 0);

    console.log('\n💡 Assessment:');

    // Speed
    if (totalDuration < 30000) console.log('✅ Speed: Excellent');
    else if (totalDuration < 60000) console.log('⚡ Speed: Good');
    else if (totalDuration < 120000) console.log('⚠️  Speed: Moderate');
    else console.log('🐌 Speed: Slow - consider optimizing');

    // Memory
    if (totalMemoryGrowth < 50)
      console.log(`✅ Memory: Excellent (${totalMemoryGrowth.toFixed(1)}MB)`);
    else if (totalMemoryGrowth < 150)
      console.log(`⚡ Memory: Good (${totalMemoryGrowth.toFixed(1)}MB)`);
    else if (totalMemoryGrowth < 300)
      console.log(`⚠️  Memory: Moderate (${totalMemoryGrowth.toFixed(1)}MB)`);
    else
      console.log(
        `🔴 Memory: High (${totalMemoryGrowth.toFixed(1)}MB) - check for leaks`
      );

    // Peak
    if (totalPeakMemory < 150)
      console.log(`✅ Peak: Efficient (${totalPeakMemory}MB)`);
    else if (totalPeakMemory < 300)
      console.log(`⚡ Peak: Acceptable (${totalPeakMemory}MB)`);
    else if (totalPeakMemory < 500)
      console.log(`⚠️  Peak: High (${totalPeakMemory}MB)`);
    else
      console.log(
        `🔴 Peak: Very High (${totalPeakMemory}MB) - optimize browser options`
      );

    console.log('');
  }
}

export default SimplePerformanceMonitor;
