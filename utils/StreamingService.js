import { STREAMING_CONFIG } from '../config.js';
import { createAIProgressLoader } from './AIProgressLoader.js';

/**
 * StreamingService class to handle progress streaming for the scraping process
 */
export class StreamingService {
  constructor(progressCallback = null) {
    this.progressCallback = progressCallback;
    this.isStreaming = progressCallback !== null;
    this.currentPhase = null;
    this.startTime = Date.now();
    this.lastHeartbeat = Date.now();
    this.phaseStartTime = null;
    this.totalPhases = 6; // Estimated number of phases
    this.currentPhaseIndex = 0;
    this.streamBuffer = [];
    this.bufferSize = STREAMING_CONFIG.bufferSize;

    this.aiProgressLoader = createAIProgressLoader(this);

    // Initialize heartbeat if streaming is enabled
    if (this.isStreaming) {
      this.startHeartbeat();
    }
  }

  /**
   * Start a new phase of the scraping process
   * @param {string} phaseName - Name of the phase
   * @param {string} description - Description of what's happening
   */
  async startPhase(phaseName, description = '') {
    this.currentPhase = phaseName;
    this.phaseStartTime = Date.now();
    this.currentPhaseIndex++;

    const progress = {
      phase: phaseName,
      description,
      status: 'started',
      progress: Math.round((this.currentPhaseIndex / this.totalPhases) * 100),
      currentPhase: this.currentPhaseIndex,
      totalPhases: this.totalPhases,
      elapsed: Date.now() - this.startTime,
      phaseStartTime: this.phaseStartTime,
    };

    await this.streamProgress(progress);
  }

  /**
   * Update progress within the current phase
   * @param {Object} update - Progress update object
   */
  async updateProgress(update) {
    if (!this.currentPhase) {
      this.log('No active phase for progress update');
      return;
    }

    const progress = {
      phase: this.currentPhase,
      status: 'progress',
      progress: Math.round((this.currentPhaseIndex / this.totalPhases) * 100),
      currentPhase: this.currentPhaseIndex,
      totalPhases: this.totalPhases,
      elapsed: Date.now() - this.startTime,
      phaseElapsed: Date.now() - this.phaseStartTime,
      ...update,
    };

    await this.streamProgress(progress);
  }

  /**
   * End the current phase
   * @param {Object} result - Phase completion result
   */
  async endPhase(result = {}) {
    if (!this.currentPhase) {
      this.log('No active phase to end');
      return;
    }

    const phaseDuration = Date.now() - this.phaseStartTime;
    const progress = {
      phase: this.currentPhase,
      status: 'completed',
      progress: Math.round((this.currentPhaseIndex / this.totalPhases) * 100),
      currentPhase: this.currentPhaseIndex,
      totalPhases: this.totalPhases,
      elapsed: Date.now() - this.startTime,
      phaseDuration,
      ...result,
    };

    await this.streamProgress(progress);
    this.currentPhase = null;
    this.phaseStartTime = null;
  }

  /**
   * Stream an error
   * @param {Error} error - Error object
   * @param {Object} context - Additional context
   */
  streamError(error, context = {}) {
    const errorData = {
      type: 'error',
      phase: this.currentPhase,
      error: {
        message: error.message,
        code: error.code || 'UNKNOWN_ERROR',
        stack: error.stack,
      },
      context,
      elapsed: Date.now() - this.startTime,
      timestamp: new Date().toISOString(),
    };

    this.streamProgress(errorData);
  }

  /**
   * Log a message with streaming
   * @param {string} message - Log message
   * @param {string} level - Log level (info, warn, error)
   */
  log(message, level = 'info') {
    const logData = {
      type: 'log',
      level,
      message,
      phase: this.currentPhase,
      elapsed: Date.now() - this.startTime,
      timestamp: new Date().toISOString(),
    };

    this.streamProgress(logData);

    // Also log to console
    console.log(`[${level.toUpperCase()}] ${message}`);
  }

  /**
   * Stream progress data
   * @param {Object} data - Progress data to stream
   */
  async streamProgress(data) {
    if (!this.isStreaming || !this.progressCallback || typeof this.progressCallback !== 'function') {
      return;
    }

    try {
      // Add default metadata
      const streamData = {
        timestamp: new Date().toISOString(),
        streamId: this.generateStreamId(),
        ...data,
      };

      // For real-time streaming, always send immediately
      // Handle both sync and async callbacks
      if (this.progressCallback) {
        const result = this.progressCallback(streamData);
        if (result && typeof result.then === 'function') {
          await result;
        }
      }

      // Also buffer if needed for backup
      if (this.bufferSize > 1) {
        this.streamBuffer.push(streamData);

        if (this.streamBuffer.length >= this.bufferSize) {
          this.flushBuffer();
        }
      }

      this.lastHeartbeat = Date.now();
    } catch (error) {
      console.error('Error streaming progress:', error);
    }
  }

  /**
   * Flush the stream buffer
   */
  flushBuffer() {
    if (this.streamBuffer.length === 0) {
      return;
    }

    try {
      // Send buffered data
      for (const data of this.streamBuffer) {
        if (this.progressCallback && typeof this.progressCallback === 'function') {
          this.progressCallback(data);
        }
      }
      this.streamBuffer = [];
    } catch (error) {
      console.error('Error flushing stream buffer:', error);
    }
  }

  /**
   * Force flush any pending data immediately
   */
  forceFlush() {
    this.flushBuffer();
  }

  /**
   * Send a heartbeat to keep the stream alive
   */
  sendHeartbeat() {
    const heartbeatData = {
      type: 'heartbeat',
      phase: this.currentPhase,
      elapsed: Date.now() - this.startTime,
      alive: true,
    };

    this.streamProgress(heartbeatData);
  }

  /**
   * Start the heartbeat interval
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeat;

      if (timeSinceLastHeartbeat > STREAMING_CONFIG.heartbeatInterval) {
        this.sendHeartbeat();
      }
    }, STREAMING_CONFIG.heartbeatInterval);
  }

  /**
   * Stop the heartbeat interval
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Complete the streaming process
   * @param {Object} finalResult - Final result data
   */
  complete(finalResult = {}) {
    // Flush any remaining buffered data
    this.flushBuffer();

    // Send completion signal
    const completionData = {
      type: 'stream_complete',
      totalElapsed: Date.now() - this.startTime,
      completedPhases: this.currentPhaseIndex,
      ...finalResult,
    };

    this.streamProgress(completionData);

    // Stop heartbeat
    this.stopHeartbeat();

    this.log('Streaming completed', 'info');
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopHeartbeat();
    this.streamBuffer = [];
    this.progressCallback = null;
  }

  /**
   * Generate a unique stream ID
   * @returns {string} Unique stream ID
   */
  generateStreamId() {
    return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get streaming statistics
   * @returns {Object} Streaming statistics
   */
  getStats() {
    return {
      isStreaming: this.isStreaming,
      currentPhase: this.currentPhase,
      currentPhaseIndex: this.currentPhaseIndex,
      totalPhases: this.totalPhases,
      elapsed: Date.now() - this.startTime,
      bufferSize: this.streamBuffer.length,
      hasHeartbeat: this.heartbeatInterval !== null,
    };
  }

  /**
   * Start an AI operation with progress indicators
   * @param {string} operationType - Type of AI operation (analyzing, converting, etc.)
   * @param {string} customMessage - Custom message to display
   * @param {string} spinnerType - Type of spinner to use
   */
  startAIOperation(
    operationType = 'processing',
    customMessage = null,
    spinnerType = 'robot'
  ) {
    this.aiProgressLoader.startOperation(
      operationType,
      customMessage,
      spinnerType
    );
  }

  /**
   * Update AI operation progress
   * @param {string} message - Progress message
   * @param {Object} additionalData - Additional data to include
   */
  updateAIProgress(message = null, additionalData = {}) {
    this.aiProgressLoader.updateProgress(message, additionalData);
  }

  /**
   * Complete the current AI operation
   * @param {string} completionMessage - Final completion message
   * @param {Object} result - Operation result data
   */
  completeAIOperation(completionMessage = null, result = {}) {
    this.aiProgressLoader.completeOperation(completionMessage, result);
  }

  /**
   * Handle AI operation error
   * @param {Error} error - Error that occurred
   * @param {string} errorMessage - Custom error message
   */
  errorAIOperation(error, errorMessage = null) {
    this.aiProgressLoader.errorOperation(error, errorMessage);
  }

  /**
   * Get AI progress callback for streaming AI operations
   * @param {string} operationType - Type of AI operation
   * @param {string} spinnerType - Type of spinner to use
   * @returns {Object} Progress callback object
   */
  getAIProgressCallback(operationType = 'processing', spinnerType = 'robot') {
    return this.aiProgressLoader.createProgressCallback(
      operationType,
      spinnerType
    );
  }

  /**
   * Check if AI operation is currently active
   * @returns {boolean} Whether AI operation is active
   */
  isAIOperationActive() {
    return this.aiProgressLoader.getStats().active;
  }

  /**
   * Get AI operation statistics
   * @returns {Object} AI operation stats
   */
  getAIStats() {
    return this.aiProgressLoader.getStats();
  }
}

/**
 * Create a new streaming service instance
 * @param {Function} progressCallback - Progress callback function
 * @returns {StreamingService} New streaming service instance
 */
export function createStreamingService(progressCallback = null) {
  return new StreamingService(progressCallback);
}
