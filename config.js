import dotenv from "dotenv";
dotenv.config();

// Environment variable validation
function validateEnvironment() {
  const errors = [];

  // Required environment variables
  if (!process.env.GOOGLE_AI_API_KEY) {
    errors.push('GOOGLE_AI_API_KEY is required');
  } else if (process.env.GOOGLE_AI_API_KEY.length < 10) {
    errors.push('GOOGLE_AI_API_KEY appears to be invalid (too short)');
  }

  // Validate numeric environment variables
  const numericVars = {
    MAX_RETRY_COUNT: process.env.MAX_RETRY_COUNT,
    RETRY_DELAY: process.env.RETRY_DELAY,
    PAGE_TIMEOUT: process.env.PAGE_TIMEOUT,
    STREAM_BUFFER_SIZE: process.env.STREAM_BUFFER_SIZE,
    STREAM_HEARTBEAT: process.env.STREAM_HEARTBEAT,
    MAX_STREAM_DURATION: process.env.MAX_STREAM_DURATION,
  };

  Object.entries(numericVars).forEach(([key, value]) => {
    if (value !== undefined && (isNaN(parseInt(value)) || parseInt(value) < 0)) {
      errors.push(`${key} must be a positive number, got: ${value}`);
    }
  });

  if (errors.length > 0) {
    console.error('❌ Environment validation errors:');
    errors.forEach(error => console.error(`   - ${error}`));
    throw new Error(`Environment validation failed: ${errors.join(', ')}`);
  }
}

// Validate environment on startup
validateEnvironment();

// Runtime environment detection
export const RUNTIME_ENV = process.env.RUNTIME_ENV || "local"; // "local" or "cloudflare"

// General configuration
export const OUTPUT_DIR = process.env.OUTPUT_DIR || null;
export const MAX_RETRY_COUNT = parseInt(process.env.MAX_RETRY_COUNT, 10) || 2;
export const RETRY_DELAY = parseInt(process.env.RETRY_DELAY, 10) || 1000;

// Playwright specific options for local environment
export const BROWSER_OPTIONS = {
  headless: true,
  ignoreHTTPSErrors: true,
  timeout: 30000, // 30 second timeout for browser operations
  args: [
    // Memory and performance optimizations
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--memory-pressure-off",
    "--max-old-space-size=2048", // Limit memory usage to 2GB
    "--start-maximized",
    
    // Performance optimizations
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-features=TranslateUI",
    "--disable-ipc-flooding-protection",
    
    // Visual rendering optimizations (since you're scraping, not viewing)
    "--disable-extensions",
    "--disable-plugins",
    // "--disable-images", // Uncomment only if you don't need images
    // "--disable-javascript", // Uncomment only if your target sites work without JS
    
    // Network optimizations
    "--aggressive-cache-discard",
    "--disable-background-networking",
    "--disable-default-apps",
    "--disable-sync",
    "--max-connections-per-host=6", // Optimize connection pooling
    "--disable-hang-monitor", // Reduce monitoring overhead
    
    // Additional performance flags
    "--no-first-run",
    "--no-zygote",
    // "--single-process", // Use with caution - may reduce stability but improves performance
    "--disable-web-security", // Only for scraping, not for testing user-facing applications
  ],
};

// Page navigation options
export const PAGE_OPTIONS = {
  waitUntil: "networkidle", // Updated to use the valid option for newer Playwright versions
  timeout: parseInt(process.env.PAGE_TIMEOUT, 10) || 10000,
};

// Streaming configuration
export const STREAMING_CONFIG = {
  enabled: process.env.STREAMING_ENABLED !== 'false',
  bufferSize: parseInt(process.env.STREAM_BUFFER_SIZE, 10) || 1, // Set to 1 for immediate streaming
  heartbeatInterval: parseInt(process.env.STREAM_HEARTBEAT, 10) || 5000, // 5 seconds
  maxStreamDuration: parseInt(process.env.MAX_STREAM_DURATION, 10) || 300000, // 5 minutes
};


export const LLM_MODEL_CONFIG = {
  apiKey: process.env.GOOGLE_AI_API_KEY || "", // Add API key to .env file
  modelName: "gemini-2.5-flash",
  smallModel: "gemini-2.5-flash",
};

